import { LocationTypes, QuestTargetAction } from "@prisma/client";

/**
 * Game event types that correspond to quest objectives and achievements
 */
export enum GameEventType {
    // Combat Events
    NPC_BATTLE_WON = "npc_battle_won",
    PVP_BATTLE_WON = "pvp_battle_won",

    // Item Events
    ITEM_CRAFTED = "item_crafted",
    ITEM_DROPPED = "item_dropped",

    // Mission & Training Events
    MISSION_COMPLETED = "mission_completed",
    STATS_TRAINED = "stats_trained",

    // Story Events
    STORY_EPISODE_COMPLETED = "story_episode_completed",

    // Social & Economy Events
    SHRINE_DONATION_MADE = "shrine_donation_made",
    BOUNTY_PLACED = "bounty_placed",
    BOUNTY_COLLECTED = "bounty_collected",
    SUGGESTION_VOTED = "suggestion_voted",

    // Adventure Events
    ENCOUNTER_COMPLETED = "encounter_completed",
    GAMBLING_PERFORMED = "gambling_performed",
    ROGUELIKE_LEVEL_REACHED = "roguelike_level_reached",

    // Quest Events
    QUEST_COMPLETED = "quest_completed",
    DAILY_QUEST_COMPLETED = "daily_quest_completed",

    // Ability Events
    ABILITY_USED = "ability_used",

    // Zone Events
    ZONE_COMPLETED = "zone_completed",
}

/**
 * Event payload interfaces for type safety
 */
export interface NPCBattleWonPayload {
    userId: number;
    creatureId: number;
    location: LocationTypes;
    turns?: number;
    damageTaken?: number;
    percentDmgTaken?: number;
    isBoss?: boolean;
}

export interface PVPBattleWonPayload {
    userId: number;
    targetId: number;
    targetLevel: number;
    targetUsername: string;
    postBattleAction?: QuestTargetAction;
}

export interface ItemCraftedPayload {
    userId: number;
    itemId: number;
    quantity: number;
}

export interface ItemDroppedPayload {
    userId: number;
    itemId: number;
    quantity: number;
    source: "battle" | "encounter" | "scavenge" | "crate" | "other";
    location?: LocationTypes;
}

export interface MissionCompletedPayload {
    userId: number;
    missionId?: number;
    hours: number;
}

export interface StatsTrainedPayload {
    userId: number;
    amount: number;
}

export interface StoryEpisodeCompletedPayload {
    userId: number;
    episodeId: number;
}

export interface ShrineDonationMadePayload {
    userId: number;
    amount: number;
}

export interface BountyPlacedPayload {
    userId: number;
    targetId: number;
    amount: number;
}

export interface BountyCollectedPayload {
    userId: number;
    bountyId: number;
    amount: number;
}

export interface SuggestionVotedPayload {
    userId: number;
    suggestionId: number;
}

export interface EncounterCompletedPayload {
    userId: number;
    encounterId: number;
    location: LocationTypes;
}

export interface GamblingPerformedPayload {
    userId: number;
    gameType: string;
    amount?: number;
}

export interface RoguelikeLevelReachedPayload {
    userId: number;
    level: number;
}

export interface QuestCompletedPayload {
    userId: number;
    questId: number;
}

export interface DailyQuestCompletedPayload {
    userId: number;
    dailyQuestId: number;
}

export interface AbilityUsedPayload {
    userId: number;
    abilityId: number;
    amount?: number;
}

export interface ZoneCompletedPayload {
    userId: number;
    zoneId: number;
    location?: LocationTypes;
}

/**
 * Map of event types to their payload types for type safety
 */
export interface GameEventPayloadMap {
    [GameEventType.NPC_BATTLE_WON]: NPCBattleWonPayload;
    [GameEventType.PVP_BATTLE_WON]: PVPBattleWonPayload;
    [GameEventType.ITEM_CRAFTED]: ItemCraftedPayload;
    [GameEventType.ITEM_DROPPED]: ItemDroppedPayload;
    [GameEventType.MISSION_COMPLETED]: MissionCompletedPayload;
    [GameEventType.STATS_TRAINED]: StatsTrainedPayload;
    [GameEventType.STORY_EPISODE_COMPLETED]: StoryEpisodeCompletedPayload;
    [GameEventType.SHRINE_DONATION_MADE]: ShrineDonationMadePayload;
    [GameEventType.BOUNTY_PLACED]: BountyPlacedPayload;
    [GameEventType.BOUNTY_COLLECTED]: BountyCollectedPayload;
    [GameEventType.SUGGESTION_VOTED]: SuggestionVotedPayload;
    [GameEventType.ENCOUNTER_COMPLETED]: EncounterCompletedPayload;
    [GameEventType.GAMBLING_PERFORMED]: GamblingPerformedPayload;
    [GameEventType.ROGUELIKE_LEVEL_REACHED]: RoguelikeLevelReachedPayload;
    [GameEventType.QUEST_COMPLETED]: QuestCompletedPayload;
    [GameEventType.DAILY_QUEST_COMPLETED]: DailyQuestCompletedPayload;
    [GameEventType.ABILITY_USED]: AbilityUsedPayload;
    [GameEventType.ZONE_COMPLETED]: ZoneCompletedPayload;
}
