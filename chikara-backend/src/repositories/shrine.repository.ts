import { db } from "../lib/db.js";
import { getNow } from "../utils/dateHelpers.js";

export const findDailyShrineGoal = async (today: Date) => {
    return await db.shrine_goal.findFirst({
        where: { goalDate: today },
        orderBy: { id: "desc" },
    });
};

export const findDailyShrineGoalWithReached = async (today: Date) => {
    return await db.shrine_goal.findFirst({
        where: {
            goalDate: today,
            goalReached: true,
        },
        orderBy: { id: "desc" },
    });
};

export const getDailyDonations = async (today: Date) => {
    return await db.shrine_donation.findMany({
        where: { date: today },
        orderBy: { updatedAt: "desc" },
        include: {
            user: {
                select: {
                    username: true,
                    avatar: true,
                },
            },
        },
    });
};

export const findUserDonationForDate = async (userId: number, date: Date) => {
    return await db.shrine_donation.findFirst({
        where: {
            userId,
            date,
        },
    });
};

export const createDonation = async (userId: number, date: Date, amount: number) => {
    return await db.shrine_donation.create({
        data: {
            userId,
            date,
            amount,
        },
    });
};

export const updateDonation = async (id: number, amount: number) => {
    return await db.shrine_donation.update({
        where: { id },
        data: { amount },
    });
};

export const updateShrineGoal = async (id: number, data: { donationAmount: number; goalReached: boolean }) => {
    return await db.shrine_goal.update({
        where: { id },
        data,
    });
};
