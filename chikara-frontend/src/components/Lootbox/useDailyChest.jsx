import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

export const useDailyChest = () => {
    const queryClient = useQueryClient();
    return useMutation(
        // eslint-disable-next-line react-hooks/react-compiler
        api.specialItems.useDailyChest.mutationOptions({
            onSuccess: () => {
                setTimeout(() => {
                    queryClient.invalidateQueries({
                        queryKey: api.user.getInventory.key(),
                    });
                }, 6000);
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error(errorMessage);
                toast.error(errorMessage);
            },
        })
    );
};
