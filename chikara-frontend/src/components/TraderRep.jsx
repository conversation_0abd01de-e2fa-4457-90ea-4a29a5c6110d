import useGameConfig from "@/hooks/useGameConfig";
import { cn } from "@/lib/utils";
import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";

export default function TraderRep({ shopId, heartWidth }) {
    const { data: traderRep } = useQuery(
        api.shops.getTraderRep.queryOptions({
            input: { shopId: Number(shopId) },
            enabled: !!shopId && !isNaN(Number(shopId)) && Number(shopId) > 0,
        })
    );
    const { MAX_TRADER_REP } = useGameConfig();

    const repAmount = (traderRep) => {
        if (!traderRep) return 1;
        const rep = traderRep?.reputationLevel;

        if (!rep || rep < 1) return 1;
        if (rep < 2) return 2;
        if (rep < 3) return 3;
        if (rep <= 4) return 4;
    };

    const getFillAmount = (traderRep, i) => {
        if (!traderRep) return "polygon(0 0, 100% 0, 100% 100%, 0 100%)";

        const rep = traderRep?.reputationLevel;

        if (!rep) return "polygon(0 0, 0% 0, 0% 100%, 0 100%)";

        const fillPercent = Math.max(0, (rep - i + 1) * 100);
        return `polygon(0 0, ${fillPercent}% 0, ${fillPercent}% 100%, 0 100%)`;
    };

    const getOpacity = (traderRep, i) => {
        if (!traderRep) return "opacity-50";

        const rep = traderRep?.reputationLevel;

        if (!rep || rep < 1) {
            if (i === 0) {
                return "opacity-100";
            } else {
                return "opacity-50";
            }
        }

        if (rep >= i) {
            return "opacity-100";
        } else {
            return "opacity-50";
        }
    };

    return (
        <>
            {[...new Array(MAX_TRADER_REP || 4)].map((e, i) => (
                <div key={i} className={cn(getOpacity(traderRep, i), "relative")}>
                    <svg
                        key={i}
                        xmlns="http://www.w3.org/2000/svg"
                        className={cn(heartWidth, "stroke-2 stroke-gray-800 text-gray-600/50 drop-shadow-md")}
                        viewBox="0 0 20 20"
                        fill="currentColor"
                    >
                        <path
                            fillRule="evenodd"
                            d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                            clipRule="evenodd"
                        />
                    </svg>
                    <svg
                        style={{ clipPath: getFillAmount(traderRep, i + 1) }}
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        className={cn(heartWidth, "absolute top-0 left-0 stroke-2 stroke-gray-800 text-pink-600")}
                    >
                        <path
                            fillRule="evenodd"
                            d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                            clipRule="evenodd"
                        />
                    </svg>
                </div>
            ))}
        </>
    );
}
