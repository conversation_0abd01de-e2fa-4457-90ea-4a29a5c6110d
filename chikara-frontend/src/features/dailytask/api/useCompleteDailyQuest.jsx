import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

export const useCompleteDailyQuest = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.dailyQuest.completeDailyQuest.mutationOptions({
            onSuccess: () => {
                toast.success("Task completed");
                queryClient.invalidateQueries({
                    queryKey: api.dailyQuest.getDailyQuests.key(),
                });
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error(errorMessage);
                toast.error(errorMessage);
            },
        })
    );
};
