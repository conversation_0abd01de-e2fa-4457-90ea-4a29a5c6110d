import gangCredImg from "@/assets/icons/UI/currency2.png";
import PurchaseItemModal from "@/features/shop/components/PurchaseItemModal";
import ShopItem from "@/features/shop/components/ShopItem";
import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";

const GangShop = ({ currentGang, currentUser }) => {
    const [itemToBuy, setItemToBuy] = useState({
        id: 0,
        cashValue: 0,
        item: {
            image: "",
        },
        name: "",
    });
    const [openModal, setOpenModal] = useState(false);

    const { data: shopInfo, isLoading } = useQuery(api.shops.shopInfo.queryOptions({ input: { shopId: 7 } }));

    const sortedListings = shopInfo?.shop_listing?.sort((a, b) => a.customCost - b.customCost);

    if (isLoading) return null;
    return (
        <div className="mx-2 flex flex-col gap-2 p-2 md:mx-6">
            {/* <Button className="w-24!" onClick={() => setViewShop(false)}>
        Back
      </Button> */}
            <div className="rounded-lg border border-gray-600 bg-slate-800 p-2">
                <div className="flex w-fit gap-1 rounded-lg border border-black bg-linear-to-b from-yellow-500 to-yellow-700 px-2 py-0.5 text-white">
                    <img src={gangCredImg} alt="" className="mx-0.5 mb-0.5 inline h-6 w-auto" />
                    <p>{currentUser?.gangCreds}</p>
                </div>
                {sortedListings?.length === 0 && (
                    <div className="flex">
                        <p className="mx-auto mt-12 text-2xl dark:text-gray-200">No items available!</p>
                    </div>
                )}
                <div className="mx-3 mt-4 mb-6 grid grid-cols-3 gap-x-3 gap-y-6 sm:grid-cols-2 sm:gap-x-6 md:mt-6 lg:grid-cols-5 xl:gap-x-3">
                    {sortedListings?.map((product) => (
                        <ShopItem
                            key={product.id}
                            product={product}
                            setOpenModal={setOpenModal}
                            setItemToBuy={setItemToBuy}
                            currencyType="gangCreds"
                        />
                    ))}
                </div>
                <PurchaseItemModal
                    openModal={openModal}
                    setOpenModal={setOpenModal}
                    itemToBuy={itemToBuy}
                    currencyType="gangCreds"
                    currentUser={currentUser}
                />
            </div>
        </div>
    );
};

export default GangShop;
