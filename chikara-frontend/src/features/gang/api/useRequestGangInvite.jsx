import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useRequestGangInvite = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.gang.requestInvite.mutationOptions({
            onSuccess: () => {
                toast.success(`Request sent!`);
                queryClient.invalidateQueries({
                    queryKey: api.gang.getGangList.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );

    const requestGangInvite = (gangId) => {
        if (!gangId) {
            toast.error("Gang ID is required");
            return;
        }

        mutation.mutate({ gangId: parseInt(gangId) });
    };

    return {
        requestGangInvite,
    };
};

export default useRequestGangInvite;
