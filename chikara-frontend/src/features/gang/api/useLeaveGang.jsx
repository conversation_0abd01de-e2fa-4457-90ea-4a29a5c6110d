import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useLeaveGang = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.gang.leaveGang.mutationOptions({
            onSuccess: () => {
                toast.success(`Gang left!`);
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );

    return {
        leaveGang: mutation.mutate,
    };
};

export default useLeaveGang;
