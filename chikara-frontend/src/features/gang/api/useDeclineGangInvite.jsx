import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useDeclineGangInvite = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.gang.declineInvite.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.gang.getCurrentInvites.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );

    return {
        declineInvite: mutation.mutate,
    };
};

export default useDeclineGangInvite;
