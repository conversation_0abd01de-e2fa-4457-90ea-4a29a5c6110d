import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useKickGangMember = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.gang.kickMember.mutationOptions({
            onSuccess: () => {
                toast.success(`User kicked from gang!`);
                queryClient.invalidateQueries({
                    queryKey: api.gang.getCurrentGang.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );

    const kickFromGang = (studentId) => {
        if (!studentId) {
            toast.error("Student ID is required");
            return;
        }

        mutation.mutate({ userId: parseInt(studentId) });
    };

    return {
        kickFromGang,
    };
};

export default useKickGangMember;
