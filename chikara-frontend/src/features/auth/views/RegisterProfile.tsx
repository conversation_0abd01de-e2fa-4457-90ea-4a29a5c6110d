import Button from "@/components/Buttons/Button";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { Link, useSearchParams } from "react-router-dom";
import OauthSection from "../components/OauthSection";
import { useCheckUsername } from "../api/useCheckUsername";

function RegisterProfile({
    registerSuccess,
    setUsername,
}: {
    registerSuccess: (_method?: string) => void;
    setUsername: (_username: string) => void;
}) {
    const [chosenName, setChosenName] = useState("");
    const [nameAvailable, setNameAvailable] = useState<boolean | null>(null);
    const [searchParams] = useSearchParams();
    const auth = searchParams.get("auth") || "local";
    const authID = searchParams.get("id") || null;

    // ORPC hook
    const checkUsernameMutation = useCheckUsername();

    const validateUsername = () => {
        if (chosenName.length > 17) {
            setNameAvailable(false);
            toast.error("Student name is too long!");
            return false;
        }
        if (chosenName.length < 3) {
            setNameAvailable(false);
            toast.error("Student name is too short!");
            return false;
        }

        return true;
    };

    const checkUsername = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        const validation = validateUsername();
        if (!validation) return;

        try {
            const response = await checkUsernameMutation.mutateAsync({ username: chosenName });
            setNameAvailable(true);
            return response;
        } catch (error: any) {
            setNameAvailable(false);
            toast.error(error.message);
            return null;
        }
    };

    const completeRegistration = async (e) => {
        // TODO: Implement complete registration
        return null;
        // e.preventDefault();

        // const validation = validateUsername();
        // if (!validation) return;

        // try {
        //     const response = await handlePost(api.auth.completeRegistration, {
        //         username: chosenName,
        //         authID,
        //     });

        //     registerSuccess(auth);
        //     return response;
        // } catch (error: any) {
        //     toast.error(error.message);
        //     return null;
        // }
    };

    return (
        <div className="px-4 pt-3 pb-6 text-shadow shadow-sm sm:px-10 md:py-8">
            <form className="space-y-6" action="#" method="POST">
                <div>
                    <label htmlFor="studentName" className="block font-medium text-gray-300 text-sm">
                        Student Name
                    </label>
                    <div className="mt-1 flex gap-2">
                        <input
                            required
                            value={chosenName}
                            id="studentName"
                            name="studentName"
                            autoComplete="username"
                            type="text"
                            className="block w-full appearance-none rounded-md border border-gray-600 bg-gray-800 px-3 py-2 text-gray-200 text-stroke-sm shadow-xs placeholder:text-gray-500 focus:border-indigo-500 focus:outline-hidden focus:ring-indigo-500 sm:text-sm"
                            onChange={(e) => {
                                setNameAvailable(null);
                                setChosenName(e.target.value);
                            }}
                        />
                        <div className="flex h-11 w-28 overflow-hidden">
                            {nameAvailable !== null ? (
                                nameAvailable ? (
                                    <div className="m-auto scale-[2] text-green-500">✔️</div>
                                ) : (
                                    <div className="m-auto scale-[2] text-red-500">❌</div>
                                )
                            ) : (
                                <Button className="m-auto" onClick={(e) => checkUsername(e)}>
                                    Check
                                </Button>
                            )}
                        </div>
                    </div>
                </div>
            </form>
            <div className="mt-6 flex w-full justify-center align-middle">
                <Button
                    disabled={!nameAvailable}
                    type="primary"
                    className="text-lg! font-medium! mx-auto w-4/5 text-stroke-sm uppercase"
                    onClick={(e) => (auth === "local" ? setUsername(chosenName) : completeRegistration(e))}
                >
                    {auth === "local" ? "Next" : "Continue"}
                </Button>
            </div>

            {auth === "local" && (
                <>
                    <div className="mt-4 text-center text-sm md:text-base">
                        Already have an account?{" "}
                        <Link className="text-blue-500 hover:brightness-125" to="/login">
                            Sign in
                        </Link>
                    </div>
                    <OauthSection />
                </>
            )}
        </div>
    );
}

export default RegisterProfile;
