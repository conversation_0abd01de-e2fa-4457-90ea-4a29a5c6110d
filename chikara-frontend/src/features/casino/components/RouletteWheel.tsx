// import { useState, useEffect } from "react";
// import { RouletteTable, useRoulette, RouletteWheel, ChipList, AvailableNumbers } from "react-casino-roulette";
// import Button from "@/components/Buttons/Button";
// import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
// import { useQueryClient, useMutation } from "@tanstack/react-query";
// import { orpc } from "@/lib/orpc";
// import "react-casino-roulette/dist/index.css";
// import { useSocketStore } from "../../../app/store/stores";
// import { api } from "@/helpers/api";

// // "react-casino-roulette": "github:dozsolti/react-casino-roulette",

// const chips = {
//     100: "https://github.com/dozsolti/react-casino-roulette/blob/main/example/public/images/blank-chips/white-chip.png?raw=true",
//     1000: "https://github.com/dozsolti/react-casino-roulette/blob/main/example/public/images/blank-chips/blue-chip.png?raw=true",
//     5000: "https://github.com/dozsolti/react-casino-roulette/blob/main/example/public/images/blank-chips/blue-chip.png?raw=true",
// };

// export default function Roulette() {
//     const { bets, onBet, total, clearBets } = useRoulette();
//     const queryClient = useQueryClient();
//     const [selectedChip, setSelectedChip] = useState(Object.keys(chips)[0]);
//     const [winningBet, setWinningBet] = useState<"-1" | AvailableNumbers>("-1");
//     const [wheelStart, setWheelStart] = useState(false);
//     const [results, setResults] = useState(null);
//     const [userBets, setUserBets] = useState(null);
//     const { data: currentUser } = useFetchCurrentUser();

//     const { socket } = useSocketStore();

//     useEffect(() => {
//         if (socket) {
//             // socket.emit("join", "roulette_spins");

//             socket.on("spin_result", (result) => {
//                 console.log(result);
//                 setWinningBet(result.winningNumber);
//                 setResults(result.results);
//                 setWheelStart(true);
//             });

//             return () => {
//                 socket.off("spin_result");
//             };
//         }
//     }, [socket]);

//     const { mutateAsync: sendBet } = useMutation(
//         orpc.casino.placeBet.mutationOptions({
//             onSuccess: () => {
//                 queryClient.invalidateQueries({
//                     queryKey: api.user.getCurrentUserInfo.key(),
//                 });
//             },
//         })
//     );

//     const placeBets = async () => {
//         try {
//             const formattedData = {};
//             Object.keys(bets).forEach(function (key: string) {
//                 formattedData[key] = bets[key].amount;
//             });

//             await sendBet(formattedData);
//             setUserBets(formattedData);
//         } catch (error) {
//             alert("Failed to place bets. Please try again.");
//         }
//     };

//     const handleEndSpin = (winner: AvailableNumbers) => {
//         setWheelStart(false);
//         if (!userBets) return;

//         const userResults = results?.filter((result) => result?.userId === currentUser?.id);

//         let totalWinnings = 0;

//         Object.values(userResults).forEach((bet) => {
//             totalWinnings += bet.amount;
//         });

//         if (totalWinnings > 0) {
//             alert(`The ball landed on ${winner}! You won ¥${totalWinnings.toLocaleString()}!`);
//         } else {
//             alert(`The ball landed on ${winner}. No winning bets.`);
//         }
//         queryClient.invalidateQueries({
//             queryKey: api.user.getCurrentUserInfo.key(),
//         });
//         setUserBets(null);
//         setResults(null);
//     };

//     return (
//         <div
//             className="flex flex-col items-center justify-center rounded-lg border border-gray-600"
//             style={{
//                 backgroundImage: `url(https://cloudflare-image.jamessut.workers.dev/ui-images/MsOfyDw.jpeg)`,
//             }}
//         >
//             <div className="mx-2 size-full bg-black/50 px-4 py-3 lg:mx-auto lg:w-fit lg:p-5">
//                 <RouletteWheel
//                     automaticSpinning
//                     start={wheelStart}
//                     winningBet={winningBet}
//                     layoutType="european"
//                     onSpinningEnd={handleEndSpin}
//                 />

//                 <RouletteTable
//                     readOnly={wheelStart}
//                     chips={chips}
//                     bets={bets}
//                     layoutType="european"
//                     height="200px"
//                     onBet={onBet(selectedChip)}
//                 />
//                 <div className="mx-6 flex justify-between gap-6 lg:justify-center lg:gap-10">
//                     <ChipList
//                         budget={currentUser?.cash}
//                         chips={chips}
//                         selectedChip={selectedChip}
//                         onChipPressed={setSelectedChip}
//                     />
//                     <div className="my-auto flex flex-col gap-2 text-xl">
//                         <p>Total Bet: {total}</p>
//                         <Button
//                             disabled={wheelStart}
//                             className="font-medium! text-base! mx-auto! my-2! w-48! text-stroke-sm"
//                             type="primary"
//                             onClick={clearBets}
//                         >
//                             Clear
//                         </Button>
//                     </div>
//                 </div>
//                 <Button
//                     disabled={wheelStart}
//                     className="font-medium! text-base! mx-auto! my-2! w-48! text-stroke-sm"
//                     type="primary"
//                     onClick={placeBets}
//                 >
//                     Place Bet
//                 </Button>
//             </div>
//         </div>
//     );
// }
