import { api, type QueryOptions } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";

export const useIsBuffActive = (buffName: string, options: QueryOptions = {}) => {
    return useQuery(
        api.shrine.isBuffActive.queryOptions({
            input: { buffName },
            staleTime: 60000, // 1 minute
            enabled: !!buffName,
            ...options,
        })
    );
};
